import { useParams } from 'react-router-dom';
import { useRecoilState, useRecoilValue } from 'recoil';
import { useSaveUserInteraction } from '~/data-provider/JobSimulation/mutations';
import { useToastContext } from '~/Providers';
import store from '~/store';

const useOpenCloseApp = () => {
  const { jobSimulationId = '' } = useParams();
  const { showToast } = useToastContext();

  const [jobSimulationApp, setJobSimulationApp] = useRecoilState(store.jobSimulationApp);
  const jobSimulationEnabledApps = useRecoilValue(store.jobSimulationEnabledApps);
  const currentMeeting = useRecoilValue(store.jobSimulationCurrentMeeting);
  const jobSimulationUser = useRecoilValue(store.jobSimulationUser);
  const saveUserInteraction = useSaveUserInteraction();

  const openApp = (appId: string, appData?: any) => {
    if (jobSimulationApp?.appId === appId) return;
    if (!jobSimulationEnabledApps.includes(appId)) {
      showToast({
        status: 'info',
        message: "You can't access this app yet.\nContinue the job simulation to unlock it.",
      });
      return;
    }
    if (appId !== 'meeting' && jobSimulationApp?.appId === 'meeting' && currentMeeting?.id) {
      showToast({
        status: 'info',
        message: 'Please finish the meeting first.',
      });
      return;
    }
    saveUserInteraction.mutate({
      jobSimulationId,
      jobSimulationEmail: jobSimulationUser?.email!,
      interaction: {
        type: 'open-app',
        appData: {
          appId: appId,
        },
      },
    });
    setJobSimulationApp({
      appId,
      ...(appData ? { data: appData } : {}),
    });
  };

  const closeApp = () => {
    if (jobSimulationApp?.appId === 'meeting' && currentMeeting?.id) {
      showToast({
        status: 'info',
        message: 'Please finish the meeting first.',
      });
      return;
    }
    saveUserInteraction.mutate({
      jobSimulationId,
      jobSimulationEmail: jobSimulationUser?.email!,
      interaction: {
        type: 'close-app',
        appData: {
          appId: jobSimulationApp?.appId || '',
        },
      },
    });
    setJobSimulationApp({ appId: '' });
  };

  return { openApp, closeApp };
};

export default useOpenCloseApp;
