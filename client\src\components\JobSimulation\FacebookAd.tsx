import { useEffect, useRef, useState } from 'react';
import { useSetRecoilState } from 'recoil';
import { SAInputField, SimulationAppScreen } from '~/common';
import {
  Input,
  Label,
  OGDialog,
  OGDialogContent,
  OGDialogFooter,
  OGDialogHeader,
  OGDialogTitle,
} from '~/components';
import store from '~/store';
import { cn } from '~/utils';

const screens: SimulationAppScreen[] = [
  {
    id: '001',
    title: '',
    image: '/assets/job-simulation/facebook-ads/001.png',
    bgColor: 'bg-[#64767e]',
    buttons: [
      {
        title: 'create 1',
        x1: 49.3,
        y1: 36.59,
        x2: 53.16,
        y2: 39.57,
        left: 49.3,
        top: 36.59,
        width: 3.86,
        height: 2.98,
        backgroundColor: 'rgba(22, 217, 97, 0.5)',
        border: 'rgba(22, 217, 97, 0.8)',
        action: { type: 'nextScreen' },
      },
      {
        title: 'create 2',
        x1: 2.74,
        y1: 15.61,
        x2: 6.71,
        y2: 18.5,
        left: 2.74,
        top: 15.61,
        width: 3.97,
        height: 2.89,
        action: { type: 'nextScreen' },
      },
    ],
  },
  {
    id: '002',
    title: '',
    image: '/assets/job-simulation/facebook-ads/002.png',
    bgColor: 'bg-[#64767e]',
    buttons: [
      {
        title: 'Choose option Awareness',
        x1: 1.29,
        y1: 24.89,
        x2: 33.14,
        y2: 30.97,
        left: 1.29,
        top: 24.89,
        width: 31.85,
        height: 6.08,
        action: { type: 'nextScreen' },
      },
    ],
  },
  {
    id: '003',
    title: '',
    image: '/assets/job-simulation/facebook-ads/003.png',
    bgColor: 'bg-[#64767e]',
    buttons: [
      {
        title: 'Continue',
        x1: 81.57,
        y1: 93.1,
        x2: 97.14,
        y2: 97.96,
        left: 81.57,
        top: 93.1,
        width: 15.57,
        height: 4.86,
        backgroundColor: 'rgba(22, 217, 97, 0.5)',
        border: 'rgba(22, 217, 97, 0.8)',
        action: { type: 'nextScreen' },
      },
    ],
  },
  {
    id: '004',
    title: '',
    image: '/assets/job-simulation/facebook-ads/004.png',
    bgColor: 'bg-[#64767e]',
    buttons: [
      {
        title: 'Next',
        x1: 74.86,
        y1: 96.14,
        x2: 77.57,
        y2: 99.03,
        left: 74.86,
        top: 96.14,
        width: 2.71,
        height: 2.89,
        backgroundColor: 'rgba(22, 217, 97, 0.5)',
        border: 'rgba(22, 217, 97, 0.8)',
        action: { type: 'nextScreen' },
      },
    ],
  },
  {
    id: '005',
    title: '',
    image: '/assets/job-simulation/facebook-ads/005.png',
    bgColor: 'bg-[#64767e]',
    buttons: [
      {
        title: 'Dropdown',
        x1: 30.43,
        y1: 26.26,
        x2: 95.0,
        y2: 31.54,
        left: 30.43,
        top: 26.26,
        width: 64.57,
        height: 5.28,
        action: {
          type: 'dropdown',
          dropdownOptions: [
            {
              label: '200,000',
              screenId: '005_01',
              dataContext:
                'Reach 200000 people. Budget: $56.33. Estimated audience size: 51.60M - 60.70M. CPM (cost per 1000 impressions): $0.18. Average Frequency: 1.55',
            },
            {
              label: '400,000',
              screenId: '005_02',
              dataContext:
                'Reach 400000 people. Budget: $113.18. Estimated audience size: 51.60M - 60.70M. CPM (cost per 1000 impressions): $0.18. Average Frequency: 1.55',
            },
            {
              label: '1,000,000',
              screenId: '005_03',
              dataContext:
                'Reach 1000000 people. Budget: $343.34. Estimated audience size: 51.60M - 60.70M. CPM (cost per 1000 impressions): $0.19. Average Frequency: 1.84',
            },
          ],
        },
      },
    ],
  },
  {
    id: '005_01',
    title: '',
    image: '/assets/job-simulation/facebook-ads/005_01.png',
    bgColor: 'bg-[#64767e]',
    buttons: [
      {
        title: 'Dropdown',
        x1: 30.86,
        y1: 9.63,
        x2: 83.86,
        y2: 14.78,
        left: 30.86,
        top: 9.63,
        width: 53.0,
        height: 5.15,
        action: {
          type: 'dropdown',
          dataContext: 'Reach minimun people',
          dropdownOptions: [
            {
              label: '200,000',
              screenId: '005_01',
              dataContext:
                'Reach 200000 people. Budget: $56.33. Estimated audience size: 51.60M - 60.70M. CPM (cost per 1000 impressions): $0.18. Average Frequency: 1.55',
            },
            {
              label: '400,000',
              screenId: '005_02',
              dataContext:
                'Reach 400000 people. Budget: $113.18. Estimated audience size: 51.60M - 60.70M. CPM (cost per 1000 impressions): $0.18. Average Frequency: 1.55',
            },
            {
              label: '1,000,000',
              screenId: '005_03',
              dataContext:
                'Reach 1000000 people. Budget: $343.34. Estimated audience size: 51.60M - 60.70M. CPM (cost per 1000 impressions): $0.19. Average Frequency: 1.84',
            },
          ],
        },
      },
      {
        title: 'Next',
        x1: 29.2,
        y1: 94.3,
        x2: 75.57,
        y2: 99.91,
        left: 29.0,
        top: 94.44,
        width: 46.57,
        height: 5.37,
        backgroundColor: 'rgba(22, 217, 97, 0.5)',
        border: 'rgba(22, 217, 97, 0.8)',
        action: { type: 'nextScreen', screenId: '006' },
      },
    ],
  },
  {
    id: '005_02',
    title: '',
    image: '/assets/job-simulation/facebook-ads/005_02.png',
    bgColor: 'bg-[#64767e]',
    buttons: [
      {
        title: 'Dropdown',
        x1: 30.86,
        y1: 9.63,
        x2: 83.86,
        y2: 14.78,
        left: 30.86,
        top: 9.63,
        width: 53.0,
        height: 5.15,
        action: {
          type: 'dropdown',
          dataContext: 'Reach minimun people',
          dropdownOptions: [
            {
              label: '200,000',
              screenId: '005_01',
              dataContext:
                'Reach 200000 people. Budget: $56.33. Estimated audience size: 51.60M - 60.70M. CPM (cost per 1000 impressions): $0.18. Average Frequency: 1.55',
            },
            {
              label: '400,000',
              screenId: '005_02',
              dataContext:
                'Reach 400000 people. Budget: $113.18. Estimated audience size: 51.60M - 60.70M. CPM (cost per 1000 impressions): $0.18. Average Frequency: 1.55',
            },
            {
              label: '1,000,000',
              screenId: '005_03',
              dataContext:
                'Reach 1000000 people. Budget: $343.34. Estimated audience size: 51.60M - 60.70M. CPM (cost per 1000 impressions): $0.19. Average Frequency: 1.84',
            },
          ],
        },
      },
      {
        title: 'Next',
        x1: 29.0,
        y1: 94.2,
        x2: 75.2,
        y2: 99.7,
        left: 29.0,
        top: 94.44,
        width: 46.57,
        height: 5.37,
        backgroundColor: 'rgba(22, 217, 97, 0.5)',
        border: 'rgba(22, 217, 97, 0.8)',
        action: { type: 'nextScreen', screenId: '006' },
      },
    ],
  },
  {
    id: '005_03',
    title: '',
    image: '/assets/job-simulation/facebook-ads/005_03.png',
    bgColor: 'bg-[#64767e]',
    buttons: [
      {
        title: 'Dropdown',
        x1: 30.86,
        y1: 9.63,
        x2: 83.86,
        y2: 14.78,
        left: 30.86,
        top: 9.63,
        width: 53.0,
        height: 5.15,
        action: {
          type: 'dropdown',
          dataContext: 'Reach minimun people',
          dropdownOptions: [
            {
              label: '200,000',
              screenId: '005_01',
              dataContext:
                'Reach 200000 people. Budget: $56.33. Estimated audience size: 51.60M - 60.70M. CPM (cost per 1000 impressions): $0.18. Average Frequency: 1.55',
            },
            {
              label: '400,000',
              screenId: '005_02',
              dataContext:
                'Reach 400000 people. Budget: $113.18. Estimated audience size: 51.60M - 60.70M. CPM (cost per 1000 impressions): $0.18. Average Frequency: 1.55',
            },
            {
              label: '1,000,000',
              screenId: '005_03',
              dataContext:
                'Reach 1000000 people. Budget: $343.34. Estimated audience size: 51.60M - 60.70M. CPM (cost per 1000 impressions): $0.19. Average Frequency: 1.84',
            },
          ],
        },
      },
      {
        title: 'Next',
        x1: 29.2,
        y1: 94.0,
        x2: 75.57,
        y2: 99.7,
        left: 29.0,
        top: 94.44,
        width: 46.57,
        height: 5.37,
        backgroundColor: 'rgba(22, 217, 97, 0.5)',
        border: 'rgba(22, 217, 97, 0.8)',
        action: { type: 'nextScreen', screenId: '006' },
      },
    ],
  },
  {
    id: '006',
    title: '',
    image: '/assets/job-simulation/facebook-ads/006.png',
    bgColor: 'bg-[#64767e]',
    buttons: [
      {
        title: 'Reserve Ad Set',
        x1: 77.29,
        y1: 87.04,
        x2: 97.57,
        y2: 95.86,
        left: 77.29,
        top: 87.04,
        width: 20.28,
        height: 8.82,
        backgroundColor: 'rgba(22, 217, 97, 0.5)',
        border: 'rgba(22, 217, 97, 0.8)',
        action: { type: 'nextScreen' },
      },
    ],
  },
  {
    id: '007',
    title: '',
    image: '/assets/job-simulation/facebook-ads/007.png',
    bgColor: 'bg-[#64767e]',
    buttons: [
      {
        title: 'Publish',
        x1: 90.29,
        y1: 96.12,
        x2: 97.71,
        y2: 99.34,
        left: 90.29,
        top: 96.12,
        width: 7.42,
        height: 3.22,
        action: {
          type: 'triggerMessage',
          message: "I've publish the ad with the following settings: ",
          withData: true,
        },
      },
    ],
  },
];

const getClickedButton = (
  screen: SimulationAppScreen,
  clickXPercent: number,
  clickYPercent: number,
) => {
  if (!screen.buttons?.length) return;
  for (const button of screen.buttons) {
    const { x1, y1, x2, y2 } = button;
    if (clickXPercent >= x1 && clickXPercent <= x2 && clickYPercent >= y1 && clickYPercent <= y2) {
      return button;
    }
  }
  return null; // No button matched
};

const getClickedInput = (
  screen: SimulationAppScreen,
  clickXPercent: number,
  clickYPercent: number,
) => {
  if (!screen.inputs?.length) return;
  for (const input of screen.inputs) {
    if (input.clickable && input.x1 && input.y1 && input.x2 && input.y2) {
      if (
        clickXPercent >= input.x1 &&
        clickXPercent <= input.x2 &&
        clickYPercent >= input.y1 &&
        clickYPercent <= input.y2
      ) {
        return input;
      }
    }
  }
  return null; // No input matched
};

const getNonClickableInputs = (screen: SimulationAppScreen) => {
  if (!screen.inputs?.length) return [];
  return screen.inputs.filter((input) => !input.clickable);
};

const FacebookAd = () => {
  const imageRef: React.MutableRefObject<HTMLImageElement | null> = useRef(null);
  const containerRef: React.MutableRefObject<HTMLDivElement | null> = useRef(null);
  const imageContainerRef = useRef<HTMLDivElement | null>(null);

  const [currentScreenIndex, setCurrentScreenIndex] = useState(0);
  const [showDialog, setShowDialog] = useState(false);
  const [dialogInputs, setDialogInputs] = useState<SAInputField[]>([]);
  const [inputValues, setInputValues] = useState<Record<string, string>>({});
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [showDropdown, setShowDropdown] = useState(false);
  const [dropdownOptions, setDropdownOptions] = useState<
    Array<{ label: string; screenId: string; dataContext?: string }>
  >([]);
  const [dropdownPosition, setDropdownPosition] = useState({ x: 0, y: 0 });
  const [userSelections, setUserSelections] = useState<string>('');
  const [currentDropdownContext, setCurrentDropdownContext] = useState<string>('');

  const setJobsimulationTriggerMessage = useSetRecoilState(store.jobSimulationTriggerMessage);

  // Reset input values and errors when screen changes
  useEffect(() => {
    if (screens[currentScreenIndex].triggerMessage)
      setJobsimulationTriggerMessage({
        message: screens[currentScreenIndex].triggerMessage,
        isTriggered: true,
      });

    setInputValues({});
    setValidationErrors({});
    setShowDialog(false);
    setShowDropdown(false);
    setCurrentDropdownContext('');
  }, [currentScreenIndex]);

  // Function to find screen index by ID
  const findScreenIndexById = (screenId: string) => {
    return screens.findIndex((screen) => screen.id === screenId);
  };

  const handleInputChange = (id: string, value: string) => {
    setInputValues((prev) => ({ ...prev, [id]: value }));
    if (validationErrors[id]) {
      setValidationErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[id];
        return newErrors;
      });
    }
  };

  // Validate inputs
  const validateInputs = () => {
    const errors: Record<string, string> = {};
    let isValid = true;

    dialogInputs.forEach((input) => {
      const value = inputValues[input.id] || '';
      if (value !== input.expectedValue) {
        errors[input.id] = `Invalid value. Expected: ${input.expectedValue}`;
        isValid = false;
      }
    });

    setValidationErrors(errors);
    return isValid;
  };

  const handleDialogSubmit = () => {
    if (validateInputs()) {
      setShowDialog(false);
      setInputValues({});
      // Move to next screen
      setCurrentScreenIndex((prevIndex) => prevIndex + 1);
    } else {
      // TODO: Show error
      console.log('Validation failed:', validationErrors);
    }
  };

  useEffect(() => {
    function handleResize() {
      if (imageRef.current && containerRef.current) {
        // const naturalWidth = imageRef.current.naturalWidth;
        // const naturalHeight = imageRef.current.naturalHeight;

        // const containerWidth = containerRef.current.clientWidth ?? 0;
        const containerHeight = containerRef.current.clientHeight ?? 0;

        // const scale = Math.min(containerWidth / naturalWidth, containerHeight / naturalHeight);

        // const scaledWidth = naturalWidth * scale;
        // const scaledHeight = naturalHeight * scale;

        imageRef.current.style.width = `auto`;
        imageRef.current.style.maxHeight = `${containerHeight}px`;

        if (imageContainerRef.current) {
          imageContainerRef.current.style.width = `auto`;
          imageContainerRef.current.style.maxHeight = `${containerHeight}px`;
        }
      }
    }

    handleResize();
  }, [imageRef.current?.src, containerRef?.current?.clientWidth, imageContainerRef]);

  const handleClickButton = (e: React.MouseEvent<HTMLImageElement>) => {
    if (!imageRef.current) return;

    const rect = imageRef.current.getBoundingClientRect();
    const clickXPercent = ((e.clientX - rect.left) / rect.width) * 100;
    const clickYPercent = ((e.clientY - rect.top) / rect.height) * 100;

    const currentScreen = screens[currentScreenIndex];
    const screenButton = getClickedButton(currentScreen, clickXPercent, clickYPercent);
    const clickedInput = getClickedInput(currentScreen, clickXPercent, clickYPercent);

    // If click a button
    if (screenButton) {
      if (screenButton.action.type === 'nextScreen') {
        if (screenButton.action.screenId) {
          const targetIndex = findScreenIndexById(screenButton.action.screenId);
          if (targetIndex !== -1) {
            setCurrentScreenIndex(targetIndex);
          }
        } else {
          setCurrentScreenIndex((prevIndex) => prevIndex + 1);
        }
      } else if (screenButton.action.type === 'dropdown' && screenButton.action.dropdownOptions) {
        // Show dropdown
        setDropdownOptions(screenButton.action.dropdownOptions);
        setDropdownPosition({
          x: (screenButton.x1 + screenButton.x2) / 2,
          y: screenButton.y2 + 2,
        });
        // setCurrentDropdownContext(screenButton.action.dataContext || '');
        setShowDropdown(true);
      } else if (screenButton.action.type === 'triggerMessage') {
        // Handle trigger message
        const message = screenButton.action.message || '';
        const finalMessage =
          screenButton.action.withData && userSelections
            ? `${message}. ${userSelections}`
            : message;

        console.log('finalMessage ::: 1 ', finalMessage);

        setJobsimulationTriggerMessage({
          message: finalMessage,
          isTriggered: true,
        });
      }
      return;
    }

    // If click an input
    if (clickedInput) {
      setDialogInputs([clickedInput]);
      setShowDialog(true);
      return;
    }

    // If the screen was clicked (not on a button or input)
    if (!screenButton && !clickedInput) {
      const nonClickableInputs = getNonClickableInputs(currentScreen);
      if (nonClickableInputs.length > 0) {
        setDialogInputs(nonClickableInputs);
        setShowDialog(true);
        return;
      }
    }

    // If the screen has a default action (like auto-advancing)
    if (currentScreen.actions?.[0]?.type === 'nextScreen') {
      setCurrentScreenIndex((prevIndex) => prevIndex + 1);
    }
  };

  const handleDropdownOptionClick = (
    screenId: string,
    optionLabel: string,
    dataContext?: string,
  ) => {
    const targetIndex = findScreenIndexById(screenId);
    if (targetIndex !== -1) {
      setCurrentScreenIndex(targetIndex);
    }

    // Save user selection
    if (dataContext) {
      // TODO: We need to set by screen or action id, so it will replace the old data
      const newSelection = `${dataContext}`;
      // setUserSelections((prev) => (prev ? `${prev}, ${newSelection}` : newSelection));
      setUserSelections(newSelection);
    }

    setShowDropdown(false);
  };

  useEffect(() => {
    if (imageRef.current) {
      imageRef.current.src = screens[currentScreenIndex].image;
    }
  }, [imageRef, currentScreenIndex]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      if (showDropdown) {
        setShowDropdown(false);
      }
    };

    if (showDropdown) {
      document.addEventListener('click', handleClickOutside);
    }

    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [showDropdown]);

  // useEffect(() => {
  //   if (screens[currentScreenIndex].triggerMessage)
  //     setJobsimulationTriggerMessage({
  //       message: screens[currentScreenIndex].triggerMessage,
  //       isTriggered: true,
  //     });
  // }, [currentScreenIndex, setJobsimulationTriggerMessage]);

  // State for tracking hover
  const [hoveredElement, setHoveredElement] = useState<{
    type: 'button' | 'input';
    index: number;
  } | null>(null);

  // Calculate positions for buttons and clickable inputs
  const getButtonStyles = (button: any): React.CSSProperties => {
    return {
      position: 'absolute' as const,
      left: `${button.x1}%`,
      top: `${button.y1}%`,
      width: `${button.x2 - button.x1}%`,
      height: `${button.y2 - button.y1}%`,
      cursor: 'pointer',
      zIndex: 10,
      backgroundColor:
        hoveredElement?.type === 'button' && hoveredElement?.index === button.index
          ? (button.backgroundColor ?? 'rgba(0, 123, 255, 0.2)')
          : 'transparent',
      border:
        hoveredElement?.type === 'button' && hoveredElement?.index === button.index
          ? (button.border ?? '2px solid rgba(0, 123, 255, 0.5)')
          : 'none',
      borderRadius: '4px',
      transition: 'all 0.2s ease',
      pointerEvents: 'auto',
    };
  };

  const getInputStyles = (input: SAInputField, index: number): React.CSSProperties | undefined => {
    if (!input.clickable || !input.x1 || !input.y1 || !input.x2 || !input.y2) return undefined;

    return {
      position: 'absolute' as const,
      left: `${input.x1}%`,
      top: `${input.y1}%`,
      width: `${input.x2 - input.x1}%`,
      height: `${input.y2 - input.y1}%`,
      cursor: 'text',
      zIndex: 10,
      backgroundColor:
        hoveredElement?.type === 'input' && hoveredElement?.index === index
          ? 'rgba(255, 193, 7, 0.2)'
          : 'transparent',
      border:
        hoveredElement?.type === 'input' && hoveredElement?.index === index
          ? '2px solid rgba(255, 193, 7, 0.5)'
          : 'none',
      borderRadius: '4px',
      transition: 'all 0.2s ease',
      pointerEvents: 'auto', // Ensure the overlay captures mouse events
    };
  };

  return (
    <>
      <div
        className={cn(
          'flex flex-col h-full items-center justify-center',
          screens[currentScreenIndex].bgColor ? `${screens[currentScreenIndex].bgColor}` : '',
        )}
        ref={containerRef}
      >
        <div className="relative" ref={imageContainerRef}>
          <img
            ref={imageRef}
            onClick={handleClickButton}
            // className="max-h-full w-auto"
            style={{ display: 'block' }} // Ensure image is block to avoid layout issues
          />

          {/* Overlay for buttons */}
          {screens[currentScreenIndex].buttons?.map((button, index) => (
            <div
              key={`button-${index}`}
              style={getButtonStyles({ ...button, index })}
              onMouseEnter={() => setHoveredElement({ type: 'button', index })}
              onMouseLeave={() => setHoveredElement(null)}
              onClick={(e) => {
                e.stopPropagation();
                if (button.action.type === 'nextScreen') {
                  if (button.action.screenId) {
                    const targetIndex = findScreenIndexById(button.action.screenId);
                    if (targetIndex !== -1) {
                      setCurrentScreenIndex(targetIndex);
                    }
                  } else {
                    setCurrentScreenIndex((prevIndex) => prevIndex + 1);
                  }
                } else if (button.action.type === 'dropdown' && button.action.dropdownOptions) {
                  // Show dropdown
                  setDropdownOptions(button.action.dropdownOptions);
                  setDropdownPosition({
                    x: (button.x1 + button.x2) / 2,
                    y: button.y2 + 2,
                  });
                  // setCurrentDropdownContext(button.action.dropdownOptions || '');
                  setShowDropdown(true);
                } else if (button.action.type === 'triggerMessage') {
                  // Handle trigger message
                  const message = button.action.message || '';
                  const finalMessage =
                    button.action.withData && userSelections
                      ? `${message}. ${userSelections}`
                      : message;

                  console.log('finalMessage ::: 2 ', finalMessage);

                  setJobsimulationTriggerMessage({
                    message: finalMessage,
                    isTriggered: true,
                  });
                }
              }}
              title={button.title}
            />
          ))}

          {/* Overlay for inputs */}
          {screens[currentScreenIndex].inputs?.map((input, index) => {
            const style = getInputStyles(input, index);
            return input.clickable && style ? (
              <div
                key={`input-${index}`}
                style={style}
                onMouseEnter={() => setHoveredElement({ type: 'input', index })}
                onMouseLeave={() => setHoveredElement(null)}
                onClick={(e) => {
                  e.stopPropagation();
                  setDialogInputs([input]);
                  setShowDialog(true);
                }}
                title={input.label}
              />
            ) : null;
          })}

          {/* Dropdown overlay */}
          {showDropdown && (
            <div
              style={{
                position: 'absolute',
                left: `${dropdownPosition.x}%`,
                top: `${dropdownPosition.y}%`,
                transform: 'translateX(-50%)',
                zIndex: 20,
                backgroundColor: 'white',
                border: '1px solid #ccc',
                borderRadius: '4px',
                boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
                minWidth: '150px',
              }}
              onClick={(e) => e.stopPropagation()}
            >
              {dropdownOptions.map((option, index) => (
                <div
                  key={index}
                  style={{
                    padding: '8px 12px',
                    cursor: 'pointer',
                    borderBottom: index < dropdownOptions.length - 1 ? '1px solid #eee' : 'none',
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#f5f5f5';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'white';
                  }}
                  onClick={() => {
                    handleDropdownOptionClick(option.screenId, option.label, option.dataContext);
                  }}
                >
                  {option.label}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Dialog for dynamic inputs  */}
      <OGDialog open={showDialog} onOpenChange={setShowDialog}>
        <OGDialogContent
          title={`${screens[currentScreenIndex].title} - Input Required`}
          className="max-w-[600px] bg-background text-text-primary shadow-2xl"
        >
          <OGDialogHeader>
            <OGDialogTitle>
              {dialogInputs.length === 1
                ? `Enter ${dialogInputs[0]?.label}`
                : 'Please enter the required information'}
            </OGDialogTitle>
          </OGDialogHeader>

          <div className="flex flex-col gap-4 py-4">
            {dialogInputs.map((input) => (
              <div key={input.id} className="flex flex-col gap-2">
                <Label htmlFor={input.id}>{input.label}</Label>
                <Input
                  id={input.id}
                  value={inputValues[input.id] || ''}
                  onChange={(e) => handleInputChange(input.id, e.target.value)}
                  className={cn(
                    validationErrors[input.id] ? 'border-red-500 focus:border-red-500' : '',
                    'transition-all duration-200',
                  )}
                  placeholder={`Enter ${input.label.toLowerCase()}`}
                />
                {validationErrors[input.id] && (
                  <p className="text-sm text-red-500">{validationErrors[input.id]}</p>
                )}
              </div>
            ))}
          </div>

          <OGDialogFooter>
            <div className="flex gap-2">
              <button
                onClick={() => setShowDialog(false)}
                className="mt-3 block w-fit rounded bg-gray-100 px-4 py-2 text-base font-semibold text-neutral-900 hover:opacity-90"
              >
                Cancel
              </button>
              <button
                onClick={handleDialogSubmit}
                className="mt-3 block w-fit rounded bg-neutral-900 px-4 py-2 text-base font-semibold text-white hover:opacity-90"
              >
                OK
              </button>
            </div>
          </OGDialogFooter>
        </OGDialogContent>
      </OGDialog>
    </>
  );
};

export default FacebookAd;
