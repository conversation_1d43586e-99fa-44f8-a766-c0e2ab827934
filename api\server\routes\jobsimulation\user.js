const express = require('express');
const router = express.Router();
const JobSimulationController = require('~/server/controllers/JobSimulationController');
const { requireJwtAuth } = require('~/server/middleware');

router.use(requireJwtAuth);

router.get('/list', JobSimulationController.getUserJobSimulationsInfo);
router.post('/:jobSimulationId', JobSimulationController.getUserJobSimulationsInfo);

module.exports = router;
