const dedent = require('dedent');
const jobSimulationESGAnalystPrompt = require('./jobSimulationESGAnalystPrompt');
const lucasJobSimulationESGAnalystPrompt = require('./lucasJobSimulationESGAnalystPrompt');
const jobSimulationDigitalMarketingPrompt = require('./jobSimulationDigitalMarketingPrompt');
const { jobSimulationPrompt, mongoDBCompassInstructions } = require('./jobSimulationPrompts')

const JobSimulationService = require('~/server/services/JobSimulation/JobSimulationService');

const prompts = {
  'esg-analyst': jobSimulationESGAnalystPrompt,
  'lucas-esg-analyst': lucasJobSimulationESGAnalystPrompt,
  'digital-marketing': jobSimulationDigitalMarketingPrompt,
};

const generateAgentJobsimulationPrompt = async (data) => {
  const { jobSimulationId = '', email = '' } = data;
  const jobSimulation = await JobSimulationService.getAdminJobSimulation(jobSimulationId);
  const jsPrompt = jobSimulationPrompt({ virtualWorld: jobSimulation.virtualWorld, credentials: jobSimulation.credentials, intakeId: jobSimulation.billionIntakeId, email });
  return dedent`
${jsPrompt}

${jobSimulation.agentInstructions || prompts[jobSimulationId] || ''}

${mongoDBCompassInstructions}
`;
};

module.exports = generateAgentJobsimulationPrompt;
