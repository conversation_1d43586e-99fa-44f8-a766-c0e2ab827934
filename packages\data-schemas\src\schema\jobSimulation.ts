import { Document, Schema } from 'mongoose';

export interface IJobSimulation extends Document {
  name: string;
  jobSimulationId: string;
  logo: string;
  companyName?: string;
  billionIntakeCode?: string;
  billionIntakeId?: string;
  agentId: string;
  agentInstructions: string;
  virtualWorld?: string;
  credentials: {
    username: string;
    password: string;
  };
}

const jobSimulationSchema: Schema<IJobSimulation> = new Schema({
  name: {
    type: String,
    required: true,
  },
  jobSimulationId: {
    type: String,
    required: true,
    index: true,
    unique: true,
  },
  logo: {
    type: String,
    required: true,
  },
  companyName: {
    type: String,
    required: false,
  },
  billionIntakeCode: {
    type: String,
    required: false,
  },
  billionIntakeId: {
    type: String,
    required: false,
  },
  agentId: {
    type: String,
    required: true,
  },
  agentInstructions: {
    type: String,
    required: true,
  },
  virtualWorld: {
    type: String,
    required: false,
  },
  credentials: {
    username: {
      type: String,
      required: true,
    },
    password: {
      type: String,
      required: true,
    },
  },
});

jobSimulationSchema.index({ jobSimulationId: 1 }, { unique: true });

export default jobSimulationSchema;
