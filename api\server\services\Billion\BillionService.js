const axios = require('axios');

const getIntakeInvitationCode = (jobSimulationId) => {
  if (jobSimulationId === 'esg-analyst') return 'SuuBjeDqnnrADsYw';
  if (jobSimulationId === 'digital-marketing') return 'okjrdEDRHQRZBFbG';
  return '';
};

const BillionService = {
  async addUserToIntake({ email, password, code, firstName, lastName }) {
    // const code = getIntakeInvitationCode(jobSimulationId);
    if (!code) {
      throw new Error('No Billion Intake Code provided.');
    }
    console.log('BillionService addUserToIntake ::: ', email, password, code, firstName, "--", lastName);

    const url = `${process.env.BILLION_API_BASE_URL}/api/v1/program-group-enrollments/join-from-ic`;
    const { data } = await axios.post(
      url,
      {
        email,
        password,
        code: [code],
        firstName,
        lastName,
      },
      {
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
      },
    );
    return data;
  },
};

module.exports = BillionService;
